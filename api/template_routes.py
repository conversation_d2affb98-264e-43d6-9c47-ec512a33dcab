"""
模板管理 API 路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from services.typst_template_manager import TypstTemplateManager, TemplateInfo
from core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/templates", tags=["templates"])


class TemplateResponse(BaseModel):
    """模板响应模型"""
    id: str
    name: str
    description: str
    preview: Optional[str] = None
    version: str
    template_type: str
    capabilities: Dict[str, Any]


class TemplateListResponse(BaseModel):
    """模板列表响应模型"""
    templates: List[TemplateResponse]
    total: int


class TemplateValidationResponse(BaseModel):
    """模板验证响应模型"""
    is_valid: bool
    errors: List[str]


class TemplateApplyRequest(BaseModel):
    """模板应用请求模型"""
    template_id: str
    content: str


class TemplateApplyResponse(BaseModel):
    """模板应用响应模型"""
    success: bool
    content: str
    message: Optional[str] = None


def get_template_manager() -> TypstTemplateManager:
    """获取模板管理器实例"""
    return TypstTemplateManager()


@router.get("/", response_model=TemplateListResponse)
async def list_templates(manager: TypstTemplateManager = Depends(get_template_manager)):
    """
    获取所有可用模板列表
    """
    try:
        templates = manager.list_templates()
        
        template_responses = []
        for template in templates:
            template_responses.append(TemplateResponse(
                id=template.id,
                name=template.name,
                description=template.description,
                preview=template.preview,
                version=template.version,
                template_type=template.template_type.value,
                capabilities={
                    "has_reimage": template.capabilities.has_reimage if template.capabilities else False,
                    "has_retable": template.capabilities.has_retable if template.capabilities else False,
                    "custom_functions": template.capabilities.has_custom_functions if template.capabilities else [],
                    "requires_imports": template.capabilities.requires_imports if template.capabilities else []
                }
            ))
        
        return TemplateListResponse(
            templates=template_responses,
            total=len(template_responses)
        )
        
    except Exception as e:
        logger.error(f"获取模板列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取模板列表失败")


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(
    template_id: str,
    manager: TypstTemplateManager = Depends(get_template_manager)
):
    """
    获取指定模板的详细信息
    """
    try:
        template = manager.get_template_info(template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail=f"模板不存在: {template_id}")
        
        return TemplateResponse(
            id=template.id,
            name=template.name,
            description=template.description,
            preview=template.preview,
            version=template.version,
            template_type=template.template_type.value,
            capabilities={
                "has_reimage": template.capabilities.has_reimage if template.capabilities else False,
                "has_retable": template.capabilities.has_retable if template.capabilities else False,
                "custom_functions": template.capabilities.has_custom_functions if template.capabilities else [],
                "requires_imports": template.capabilities.requires_imports if template.capabilities else []
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取模板信息失败")


@router.get("/{template_id}/validate", response_model=TemplateValidationResponse)
async def validate_template(
    template_id: str,
    manager: TypstTemplateManager = Depends(get_template_manager)
):
    """
    验证指定模板
    """
    try:
        is_valid, errors = manager.validate_template(template_id)
        
        return TemplateValidationResponse(
            is_valid=is_valid,
            errors=errors
        )
        
    except Exception as e:
        logger.error(f"验证模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail="验证模板失败")


@router.get("/{template_id}/content")
async def get_template_content(
    template_id: str,
    manager: TypstTemplateManager = Depends(get_template_manager)
):
    """
    获取模板的原始内容
    """
    try:
        template = manager.get_template_info(template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail=f"模板不存在: {template_id}")
        
        content = manager.get_template_content(template)
        
        return {
            "template_id": template_id,
            "content": content,
            "content_length": len(content)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取模板内容失败")


@router.post("/apply", response_model=TemplateApplyResponse)
async def apply_template(
    request: TemplateApplyRequest,
    manager: TypstTemplateManager = Depends(get_template_manager)
):
    """
    将模板应用到指定内容
    """
    try:
        template = manager.get_template_info(request.template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail=f"模板不存在: {request.template_id}")
        
        # 验证模板
        is_valid, errors = manager.validate_template(request.template_id)
        if not is_valid:
            return TemplateApplyResponse(
                success=False,
                content=request.content,
                message=f"模板验证失败: {'; '.join(errors)}"
            )
        
        # 应用模板
        result_content = manager.apply_template(template, request.content)
        
        return TemplateApplyResponse(
            success=True,
            content=result_content,
            message=f"成功应用模板: {template.name}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"应用模板失败: {str(e)}")
        return TemplateApplyResponse(
            success=False,
            content=request.content,
            message=f"应用模板失败: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """
    模板系统健康检查
    """
    try:
        manager = TypstTemplateManager()
        templates = manager.list_templates()
        
        # 验证所有模板
        valid_count = 0
        total_count = len(templates)
        
        for template in templates:
            is_valid, _ = manager.validate_template(template.id)
            if is_valid:
                valid_count += 1
        
        return {
            "status": "healthy",
            "total_templates": total_count,
            "valid_templates": valid_count,
            "invalid_templates": total_count - valid_count,
            "template_manager_version": "2.0.0"
        }
        
    except Exception as e:
        logger.error(f"模板系统健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

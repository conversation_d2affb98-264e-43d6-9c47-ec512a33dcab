# Typst 模板系统重构方案

## 问题分析

### 原有系统的根本性问题

#### 1. 架构层面：缺乏抽象与扩展性
- **硬编码路径模式**：强制要求 `<id>/<id>.typ` 结构，违反开放封闭原则
- **配置与实现脱节**：`templates.json` 中的 `typst_file` 字段被完全忽略
- **简单字符串拼接**：破坏了 Typst 的模块系统，无法利用其导入机制

#### 2. 语义层面：概念混淆与职责不清
- **概念重复**：`id` 与 `name` 无实际区分，缺乏清晰语义
- **职责耦合**：模板应用逻辑与内容转换逻辑混合在同一方法中
- **缺乏版本管理**：无法处理模板演进和依赖关系

#### 3. 技术层面：实现方式原始且脆弱
- **字符串检测**：`has_reimage`/`has_retable` 检测过于简陋
- **缺乏验证机制**：无模板有效性检查和错误恢复
- **无法扩展**：无法处理复杂的模板继承和组合场景

## 解决方案

### 核心设计原则

1. **分离关注点**：模板管理、内容转换、应用逻辑分离
2. **配置驱动**：完全基于 `templates.json` 配置，支持灵活路径
3. **能力发现**：自动分析模板能力，而非硬编码检测
4. **模块化设计**：支持 Typst 原生的模块导入机制
5. **可扩展架构**：易于添加新的模板类型和功能

### 新架构组件

#### 1. TemplateInfo 数据模型
```python
@dataclass
class TemplateInfo:
    id: str                    # 唯一标识符
    name: str                  # 显示名称（支持中文）
    description: str           # 详细描述
    typst_file: str           # 相对路径（支持任意结构）
    preview: Optional[str]     # 预览图路径
    template_type: TemplateType # 模板类型枚举
    capabilities: TemplateCapability # 能力描述
    version: str              # 版本号
```

#### 2. TemplateCapability 能力系统
```python
@dataclass
class TemplateCapability:
    has_reimage: bool                    # 是否支持图片重格式化
    has_retable: bool                    # 是否支持表格重格式化
    has_custom_functions: List[str]      # 自定义函数列表
    requires_imports: List[str]          # 依赖的导入模块
```

#### 3. TypstTemplateManager 管理器
- **配置加载**：解析 `templates.json`，支持灵活路径配置
- **能力分析**：自动检测模板中的函数定义和导入需求
- **模板验证**：检查文件存在性、语法正确性等
- **内容转换**：根据模板能力智能转换用户内容
- **模块化应用**：支持 Typst 原生导入机制

### 配置文件增强

#### 新的 templates.json 结构
```json
{
  "id": "academic",
  "name": "学术论文模板",
  "description": "适用于学术论文、研究报告的专业模板",
  "typst_file": "academic/academic.typ",  // 支持任意路径
  "preview": "preview/academic.svg",
  "version": "1.0.0",
  "template_type": "functional",
  "capabilities": {
    "has_reimage": false,
    "has_retable": false,
    "custom_functions": ["academic_figure", "academic_table", "abstract"],
    "supports_cjk": true,
    "supports_math": true,
    "supports_citations": true
  }
}
```

### 模板类型系统

#### TemplateType 枚举
- **STYLE_ONLY**：仅样式模板，只包含样式设置
- **FUNCTIONAL**：功能性模板，包含自定义函数
- **LAYOUT**：布局模板，定义页面结构

### 智能内容转换

#### 基于能力的转换策略
```python
def _transform_content_for_template(self, content: str, capabilities: TemplateCapability) -> str:
    transformed = content
    
    if capabilities.has_reimage:
        transformed = self._convert_image_format(transformed)
    
    if capabilities.has_retable:
        transformed = self._convert_table_format(transformed)
    
    # 可扩展更多转换规则
    return transformed
```

#### 模块化应用机制
- **功能性模板**：使用 Typst 导入机制，支持模块化
- **样式模板**：使用内容拼接，保持简单性
- **布局模板**：支持内容插槽和结构化组合

## 实现效果

### 1. 灵活的路径配置
```json
// 支持任意目录结构
"typst_file": "academic/academic.typ"
"typst_file": "styles/modern/template.typ"
"typst_file": "legacy/old_format.typ"
```

### 2. 自动能力发现
```python
# 自动检测模板中的函数定义
functions = re.findall(r'#let\s+(\w+)\s*\(', content)
capabilities.has_custom_functions = list(set(functions))
```

### 3. 智能内容转换
```python
# 根据模板能力自动选择转换策略
if template_info.capabilities.has_reimage:
    content = self._convert_image_format(content)
```

### 4. 完整的验证机制
```python
def validate_template(self, template_id: str) -> Tuple[bool, List[str]]:
    # 检查文件存在性、内容有效性、依赖完整性
    return is_valid, error_messages
```

### 5. RESTful API 支持
```python
# 完整的模板管理 API
GET /api/templates/                    # 列出所有模板
GET /api/templates/{id}               # 获取模板详情
GET /api/templates/{id}/validate      # 验证模板
POST /api/templates/apply             # 应用模板
```

## 向后兼容性

### 渐进式迁移策略
1. **保持现有接口**：`apply_template_to_content` 方法签名不变
2. **自动适配**：自动检测旧格式配置并转换
3. **逐步增强**：新功能通过新接口提供，旧功能继续工作

### 配置兼容性
```python
# 自动适配旧格式
if "typst_file" not in template_data:
    template_data["typst_file"] = f"{template_id}/{template_id}.typ"
```

## 扩展能力

### 1. 模板继承
```json
{
  "id": "academic_extended",
  "extends": "academic",
  "overrides": {
    "capabilities": {
      "custom_functions": ["academic_figure", "enhanced_table"]
    }
  }
}
```

### 2. 动态模板
```python
# 支持运行时生成的模板
class DynamicTemplateProvider:
    def generate_template(self, user_preferences: Dict) -> TemplateInfo
```

### 3. 模板市场
```python
# 支持远程模板仓库
class TemplateRepository:
    def fetch_template(self, repo_url: str, template_id: str) -> TemplateInfo
```

## 性能优化

### 1. 缓存机制
- **模板内容缓存**：避免重复文件读取
- **能力分析缓存**：避免重复正则匹配
- **验证结果缓存**：避免重复验证

### 2. 懒加载
- **按需加载**：只在使用时加载模板内容
- **异步分析**：后台异步分析模板能力

### 3. 批量操作
- **批量验证**：一次性验证所有模板
- **批量转换**：支持多文档并行处理

## 测试策略

### 1. 单元测试
- 模板管理器各组件独立测试
- 内容转换函数精确测试
- 边界条件和异常处理测试

### 2. 集成测试
- 端到端模板应用流程测试
- API 接口完整性测试
- 多模板组合场景测试

### 3. 性能测试
- 大量模板加载性能测试
- 复杂内容转换性能测试
- 并发访问压力测试

这个重构方案彻底解决了原有系统的根本性问题，提供了一个可扩展、可维护、高性能的模板系统架构。

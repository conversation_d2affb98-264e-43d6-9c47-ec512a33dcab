#!/usr/bin/env python3
"""
测试新的模板系统
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.typst_template_manager import TypstTemplateManager
from services.markdown_to_typst_service import MarkdownToTypstService


def test_template_manager():
    """测试模板管理器"""
    print("=== 测试模板管理器 ===")
    
    manager = TypstTemplateManager()
    
    # 列出所有模板
    templates = manager.list_templates()
    print(f"发现 {len(templates)} 个模板:")
    for template in templates:
        print(f"  - {template.id}: {template.name}")
        print(f"    描述: {template.description}")
        print(f"    类型: {template.template_type.value}")
        print(f"    版本: {template.version}")
        if template.capabilities:
            print(f"    能力: reimage={template.capabilities.has_reimage}, retable={template.capabilities.has_retable}")
            print(f"    自定义函数: {template.capabilities.has_custom_functions}")
        print()
    
    # 测试模板验证
    print("=== 测试模板验证 ===")
    for template in templates:
        is_valid, errors = manager.validate_template(template.id)
        print(f"模板 {template.id}: {'有效' if is_valid else '无效'}")
        if errors:
            for error in errors:
                print(f"  错误: {error}")
        print()


def test_template_application():
    """测试模板应用"""
    print("=== 测试模板应用 ===")
    
    # 创建测试内容
    test_content = """
= 我的旅行报告

== 第一天：抵达北京

今天我们抵达了北京，天气很好。

#image("beijing.jpg")
图1 北京天安门广场

== 行程安排

表1 行程安排
#table(
  columns: 3,
  [时间], [地点], [活动],
  [9:00], [天安门], [参观],
  [14:00], [故宫], [游览],
  [18:00], [王府井], [购物]
);

== 总结

这次旅行很愉快。
"""
    
    service = MarkdownToTypstService()
    manager = TypstTemplateManager()
    
    # 测试不同模板
    templates = manager.list_templates()
    
    for template in templates:
        print(f"--- 应用模板: {template.name} ---")
        try:
            result = service.apply_template_to_content(template.id, test_content)
            print(f"应用成功，内容长度: {len(result)}")
            
            # 显示前几行结果
            lines = result.split('\n')[:10]
            print("前10行内容:")
            for i, line in enumerate(lines, 1):
                print(f"  {i:2d}: {line}")
            print("  ...")
            
        except Exception as e:
            print(f"应用失败: {e}")
        print()


def test_content_transformation():
    """测试内容转换"""
    print("=== 测试内容转换 ===")
    
    manager = TypstTemplateManager()
    
    # 测试图片转换
    image_content = '#image("test.jpg"); 图1 测试图片'
    print(f"原始图片内容: {image_content}")
    
    converted = manager._convert_image_format(image_content)
    print(f"转换后: {converted}")
    print()
    
    # 测试表格转换
    table_content = '''表1 测试表格
#table(
  columns: 2,
  [A], [B],
  [1], [2]
);'''
    print(f"原始表格内容: {table_content}")
    
    converted = manager._convert_table_format(table_content)
    print(f"转换后: {converted}")
    print()


def main():
    """主函数"""
    print("开始测试新的模板系统...")
    print()
    
    try:
        test_template_manager()
        test_template_application()
        test_content_transformation()
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

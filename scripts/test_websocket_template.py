#!/usr/bin/env python3
"""
测试 WebSocket + 模板系统的完整流程
"""

import asyncio
import json
import websockets
import base64
from pathlib import Path


async def test_websocket_template_system():
    """测试 WebSocket 模板系统"""
    uri = "ws://localhost:8000/api/ws/compile/test_session"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket 连接成功")
            
            # 1. 接收连接确认消息
            response = await websocket.recv()
            message = json.loads(response)
            print(f"📨 连接确认: {message['type']}")
            
            # 2. 请求模板列表
            print("\n🔍 请求模板列表...")
            await websocket.send(json.dumps({
                'type': 'list_templates'
            }))
            
            response = await websocket.recv()
            message = json.loads(response)
            
            if message['type'] == 'templates_list':
                templates = message['templates']
                print(f"✅ 获取到 {len(templates)} 个模板:")
                for template in templates:
                    print(f"  - {template['id']}: {template['name']}")
                    print(f"    描述: {template['description']}")
                    print(f"    文件: {template['typst_file']}")
                print()
            else:
                print(f"❌ 获取模板列表失败: {message}")
                return
            
            # 3. 测试不同模板的编译
            test_markdown = """# 我的旅行报告

## 第一天：抵达北京

今天我们抵达了北京，天气很好。

## 行程安排

| 时间 | 地点 | 活动 |
|------|------|------|
| 9:00 | 天安门 | 参观 |
| 14:00 | 故宫 | 游览 |
| 18:00 | 王府井 | 购物 |

## 总结

这次旅行很愉快。
"""
            
            for template in templates:
                template_id = template['id']
                print(f"🚀 测试模板: {template_id}")
                
                # 发送编译请求
                await websocket.send(json.dumps({
                    'type': 'compile',
                    'content': test_markdown,
                    'template_name': template_id
                }))
                
                # 接收编译过程消息
                compilation_complete = False
                while not compilation_complete:
                    response = await websocket.recv()
                    message = json.loads(response)
                    
                    if message['type'] == 'compilation_started':
                        print("  📝 编译开始...")
                        
                    elif message['type'] == 'compilation_progress':
                        stage = message['stage']
                        progress = message['progress']
                        print(f"  ⏳ 编译进度: {stage} ({progress*100:.0f}%)")
                        
                    elif message['type'] == 'compilation_complete':
                        result = message['result']
                        vector_data = base64.b64decode(result['vector_data'])
                        print(f"  ✅ 编译完成! 向量数据大小: {len(vector_data)} 字节")
                        
                        # 保存结果文件
                        output_file = Path(f"tmp/test_{template_id}_output.sir.in")
                        output_file.parent.mkdir(exist_ok=True)
                        with open(output_file, 'wb') as f:
                            f.write(vector_data)
                        print(f"  💾 结果已保存到: {output_file}")
                        
                        compilation_complete = True
                        
                    elif message['type'] == 'compilation_error':
                        print(f"  ❌ 编译失败: {message['error']}")
                        compilation_complete = True
                
                print()
            
            # 4. 测试 ping-pong
            print("🏓 测试 ping-pong...")
            await websocket.send(json.dumps({'type': 'ping'}))
            response = await websocket.recv()
            message = json.loads(response)
            if message['type'] == 'pong':
                print("✅ ping-pong 正常")
            else:
                print(f"❌ ping-pong 异常: {message}")
            
            print("\n🎉 所有测试完成!")
            
    except websockets.exceptions.ConnectionRefused:
        print("❌ 无法连接到 WebSocket 服务器")
        print("请确保服务器正在运行: python main.py")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_template_validation():
    """测试模板文件验证"""
    print("🔍 验证模板文件...")
    
    project_root = Path(__file__).parent.parent
    templates_file = project_root / "assets" / "templates" / "templates.json"
    
    if not templates_file.exists():
        print("❌ 模板配置文件不存在")
        return False
    
    try:
        with open(templates_file, 'r', encoding='utf-8') as f:
            templates = json.load(f)
        
        print(f"✅ 模板配置文件加载成功，包含 {len(templates)} 个模板")
        
        all_valid = True
        for template in templates:
            template_id = template['id']
            typst_file = template['typst_file']
            template_path = project_root / "assets" / "templates" / typst_file
            
            if template_path.exists():
                print(f"  ✅ {template_id}: {template_path}")
            else:
                print(f"  ❌ {template_id}: {template_path} (文件不存在)")
                all_valid = False
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("开始测试 WebSocket + 模板系统...")
    print("=" * 50)
    
    # 首先验证模板文件
    if not asyncio.run(test_template_validation()):
        print("❌ 模板文件验证失败，请检查模板配置")
        return
    
    print("\n" + "=" * 50)
    print("开始 WebSocket 测试...")
    
    # 然后测试 WebSocket 功能
    asyncio.run(test_websocket_template_system())


if __name__ == "__main__":
    main()

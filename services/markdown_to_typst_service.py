"""
Markdown转Typst服务
使用cmarker库将Markdown文件转换为Typst格式
"""

import os
import json
import tempfile
from typing import Optional
from pathlib import Path
from core.logging import get_logger
from config import settings

# 导入extract_cmarker_source库
import cmarker_python

glogger = get_logger(__name__)


class MarkdownToTypstService:
    """Markdown转Typst服务"""

    def __init__(self):
        # 目前不需要特别的配置
        pass

    def convert_markdown_to_typst(self, markdown_content: str) -> str:
        """
        将Markdown内容转换为Typst格式

        Args:
            markdown_content: Markdown内容

        Returns:
            str: 转换后的Typst内容
        """
        try:
            glogger.info(f"开始转换Markdown到Typst，内容长度: {len(markdown_content)}")

            # 调用cmarker库进行转换
            # 参考lib.rs中的API定义
            # 使用convert_md_to_typ函数，需要提供输入和输出文件路径
            # 由于我们已经读取了内容，这里我们创建临时文件来处理
            with tempfile.NamedTemporaryFile(suffix=".md", delete=False, mode="w", encoding="utf-8") as tmp_input:
                tmp_input.write(markdown_content)
                tmp_input_path = tmp_input.name

            with tempfile.NamedTemporaryFile(suffix=".typst", delete=False, mode="w", encoding="utf-8") as tmp_output:
                tmp_output_path = tmp_output.name

            try:
                # 调用cmarker_python的convert_md_to_typ函数
                cmarker_python.convert_md_to_typ(tmp_input_path, tmp_output_path) # type: ignore

                # 读取转换后的内容
                with open(tmp_output_path, 'r', encoding='utf-8') as f:
                    typst_content = f.read()
            finally:
                # 清理临时文件
                os.remove(tmp_input_path)
                os.remove(tmp_output_path)

            glogger.info(f"Markdown转换完成，生成Typst内容长度: {len(typst_content)}")
            return typst_content
        except Exception as e:
            glogger.error(f"Markdown转换Typst失败: {str(e)}")
            raise

    def convert_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """
        转换Markdown文件为Typst文件

        Args:
            file_path: Markdown文件路径
            output_path: 输出Typst文件路径，可选

        Returns:
            str: 输出文件路径
        """
        try:
            glogger.info(f"开始转换文件: {file_path}")

            # 读取Markdown文件
            with open(file_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 转换内容
            typst_content = self.convert_markdown_to_typst(markdown_content)

            # 确定输出路径
            if not output_path:
                # 默认输出路径
                md_filename = Path(file_path).stem
                output_path = f"{md_filename}.typ"

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

            # 写入Typst文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(typst_content)

            glogger.info(f"转换结果已保存到: {output_path}")
            return output_path
        except Exception as e:
            glogger.error(f"文件转换失败: {str(e)}")
            raise

    async def convert_file_async(self, file_path: str, output_path: Optional[str] = None) -> str:
        """
        异步版本的文件转换

        Args:
            file_path: Markdown文件路径
            output_path: 输出Typst文件路径，可选

        Returns:
            str: 输出文件路径
        """
        # 注意：这里实际上是同步操作，但为了保持API一致性，提供异步接口
        return self.convert_file(file_path, output_path)
        
    
    def apply_template_to_content(self, template_id: str, typst_content: str) -> str:
        """
        将指定模板应用到传入的 Typst 内容。
        使用 Typst 原生的导入机制，简单而可靠。
        """
        try:
            project_root = Path(__file__).parent.parent
            templates_config = project_root / "assets" / "templates" / "templates.json"

            # 读取模板配置
            if not templates_config.exists():
                glogger.error(f"模板配置文件不存在: {templates_config}")
                return typst_content

            with open(templates_config, 'r', encoding='utf-8') as f:
                templates = json.load(f)

            # 查找模板
            template_info = None
            for template in templates:
                if template["id"] == template_id:
                    template_info = template
                    break

            if not template_info:
                glogger.error(f"模板不存在: {template_id}")
                return typst_content

            # 构建模板文件路径
            template_file = project_root / "assets" / "templates" / template_info["typst_file"]
            if not template_file.exists():
                glogger.error(f"模板文件不存在: {template_file}")
                return typst_content

            # 使用 Typst 原生导入方式
            # 生成相对路径（从用户内容到模板文件）
            relative_path = f"../../assets/templates/{template_info['typst_file']}"

            # 构建最终内容：导入模板 + 应用模板 + 用户内容
            final_content = f'''#import "{relative_path}": template

#show: template

{typst_content}'''

            glogger.info(f"成功应用模板: {template_info['name']}")
            return final_content

        except Exception as e:
            glogger.error(f"应用模板到内容失败: {str(e)}")
            return typst_content
            
    def _convert_image_format(self, content: str) -> str:
        """
        转换用户文件中的图片格式：#image(...)图n 图片描述 -> #reimage(image(...), 图片描述);
        或 #image(...); 图n 图片描述 -> #reimage(image(...), 图片描述);
        
        Args:
            content: 用户Typst文件内容
        
        Returns:
            str: 转换后的内容
        """
        import re
        
        # 匹配图片格式：#image(...) 后面可能跟着分号和空格，然后是 图n 图片描述
        # 1. 先匹配完整的#image(...)表达式
        # 2. 处理可能存在的分号和空格
        # 3. 提取图n后第一个空格和第二个空格之间的文本作为图名，限制长度为20个字符
        # 如果找不到图名，image_desc为空
        pattern = r'(#image\([^)]*\))\s*;?\s*(?:图\d+\s+([^\s]{1,20}))'
        
        def replace_image(match):
            image_content = match.group(1)  # 完整的image函数调用，包括括号
            image_desc = match.group(2)  # 图片描述文本，可能为None
            
            # 如果找不到图片描述，设为空字符串
            if image_desc is None:
                image_desc = ""
            else:
                image_desc = image_desc.strip()  # 移除首尾空格
            
            # 提取image函数内部的参数内容（去掉#image(和最后的)）
            image_params = image_content[7:-1]  # 从#image(开始到最后的)结束
            
            # 转换为 #reimage(image(...), 图片描述); 格式，并确保添加分号和空格
            return f'#reimage(image({image_params}), "{image_desc}"); '
        
        # 使用正则替换函数处理所有匹配项
        result = re.sub(pattern, replace_image, content)
        
        return result
        
    def _convert_table_format(self, content: str) -> str:
        """
        转换用户文件中的表格格式：表n 表格描述#table(...) -> #retable("表格描述", table(...));
        
        Args:
            content: 用户Typst文件内容
            
        Returns:
            str: 转换后的内容
        """
        import re
        
        # 匹配表格格式：表n 表格描述#table(...)
        # 1. 匹配 表n 结构
        # 2. 提取表n后第一个空格和第二个空格之间的文本作为表名，限制长度为20个字符
        # 如果找不到表名，table_desc为空
        pattern = r'表(\d+)\s+([^\s]{1,20})\s*(?:[^#]+?#table\(.*?\);)'
        
        def replace_table(match):
            table_num = match.group(1)
            table_desc = match.group(2)
            full_match = match.group(0)
            
            # 处理table_desc可能为None的情况
            if table_desc is None:
                table_desc = ""
            else:
                table_desc = table_desc.strip()
            
            # 提取#table(...)内容
            table_start = full_match.find("#table(")
            table_end = full_match.rfind(");") + 1
            table_content = full_match[table_start:table_end]
            
            # 构建新的表格格式
            return f'#retable("{table_desc}", table{table_content[6:-1]})); '
        
        # 使用正则表达式进行替换
        result = re.sub(pattern, replace_table, content)
        return result

"""
Typst 模板管理器 - 提供模板的加载、验证、应用等功能
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from core.logging import get_logger

glogger = get_logger(__name__)


class TemplateType(Enum):
    """模板类型枚举"""
    STYLE_ONLY = "style_only"  # 仅样式模板
    FUNCTIONAL = "functional"   # 功能性模板（包含自定义函数）
    LAYOUT = "layout"          # 布局模板


@dataclass
class TemplateCapability:
    """模板能力描述"""
    has_reimage: bool = False
    has_retable: bool = False
    has_custom_functions: List[str] = None
    requires_imports: List[str] = None
    
    def __post_init__(self):
        if self.has_custom_functions is None:
            self.has_custom_functions = []
        if self.requires_imports is None:
            self.requires_imports = []


@dataclass
class TemplateInfo:
    """模板信息数据类"""
    id: str
    name: str
    description: str
    typst_file: str
    preview: Optional[str] = None
    template_type: TemplateType = TemplateType.STYLE_ONLY
    capabilities: Optional[TemplateCapability] = None
    version: str = "1.0.0"
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = TemplateCapability()


class TypstTemplateManager:
    """Typst 模板管理器"""
    
    def __init__(self, templates_dir: Optional[Path] = None):
        """
        初始化模板管理器
        
        Args:
            templates_dir: 模板目录路径，默认为项目根目录下的 assets/templates
        """
        if templates_dir is None:
            project_root = Path(__file__).parent.parent
            templates_dir = project_root / "assets" / "templates"
        
        self.templates_dir = templates_dir
        self.templates_config_file = templates_dir / "templates.json"
        self._templates_cache: Dict[str, TemplateInfo] = {}
        self._load_templates()
    
    def _load_templates(self) -> None:
        """加载模板配置"""
        try:
            if not self.templates_config_file.exists():
                glogger.warning(f"模板配置文件不存在: {self.templates_config_file}")
                return
            
            with open(self.templates_config_file, 'r', encoding='utf-8') as f:
                templates_data = json.load(f)
            
            for template_data in templates_data:
                template_info = self._parse_template_data(template_data)
                if template_info:
                    # 分析模板能力
                    self._analyze_template_capabilities(template_info)
                    self._templates_cache[template_info.id] = template_info
                    
            glogger.info(f"成功加载 {len(self._templates_cache)} 个模板")
            
        except Exception as e:
            glogger.error(f"加载模板配置失败: {str(e)}")
    
    def _parse_template_data(self, data: Dict[str, Any]) -> Optional[TemplateInfo]:
        """解析模板数据"""
        try:
            return TemplateInfo(
                id=data["id"],
                name=data.get("name", data["id"]),
                description=data.get("description", ""),
                typst_file=data["typst_file"],
                preview=data.get("preview"),
                version=data.get("version", "1.0.0")
            )
        except KeyError as e:
            glogger.error(f"模板数据缺少必要字段 {e}: {data}")
            return None
    
    def _analyze_template_capabilities(self, template_info: TemplateInfo) -> None:
        """分析模板能力"""
        try:
            template_path = self.templates_dir / template_info.typst_file
            if not template_path.exists():
                glogger.warning(f"模板文件不存在: {template_path}")
                return
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            capabilities = TemplateCapability()
            
            # 检测自定义函数
            capabilities.has_reimage = "#let reimage(" in content
            capabilities.has_retable = "#let retable(" in content
            
            # 提取所有自定义函数
            function_pattern = r'#let\s+(\w+)\s*\('
            functions = re.findall(function_pattern, content)
            capabilities.has_custom_functions = list(set(functions))
            
            # 检测导入需求
            import_pattern = r'#import\s+"([^"]+)"'
            imports = re.findall(import_pattern, content)
            capabilities.requires_imports = imports
            
            # 确定模板类型
            if capabilities.has_custom_functions:
                template_info.template_type = TemplateType.FUNCTIONAL
            else:
                template_info.template_type = TemplateType.STYLE_ONLY
            
            template_info.capabilities = capabilities
            
        except Exception as e:
            glogger.error(f"分析模板能力失败: {str(e)}")
    
    def get_template_info(self, template_id: str) -> Optional[TemplateInfo]:
        """获取模板信息"""
        return self._templates_cache.get(template_id)
    
    def list_templates(self) -> List[TemplateInfo]:
        """列出所有模板"""
        return list(self._templates_cache.values())
    
    def get_template_content(self, template_info: TemplateInfo) -> str:
        """获取模板内容"""
        try:
            template_path = self.templates_dir / template_info.typst_file
            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                return f.read()
                
        except Exception as e:
            glogger.error(f"读取模板内容失败: {str(e)}")
            raise
    
    def apply_template(self, template_info: TemplateInfo, content: str) -> str:
        """
        应用模板到内容
        
        Args:
            template_info: 模板信息
            content: 要应用模板的内容
            
        Returns:
            str: 应用模板后的内容
        """
        try:
            template_content = self.get_template_content(template_info)
            
            # 根据模板能力转换内容
            transformed_content = self._transform_content_for_template(
                content, template_info.capabilities
            )
            
            # 使用模块化导入方式而非简单拼接
            if template_info.template_type == TemplateType.FUNCTIONAL:
                # 对于功能性模板，使用导入方式
                return self._apply_functional_template(template_content, transformed_content)
            else:
                # 对于样式模板，使用拼接方式
                return template_content + "\n\n" + transformed_content
                
        except Exception as e:
            glogger.error(f"应用模板失败: {str(e)}")
            return content
    
    def _transform_content_for_template(self, content: str, capabilities: TemplateCapability) -> str:
        """根据模板能力转换内容"""
        transformed = content
        
        if capabilities.has_reimage:
            transformed = self._convert_image_format(transformed)
        
        if capabilities.has_retable:
            transformed = self._convert_table_format(transformed)
        
        return transformed
    
    def _apply_functional_template(self, template_content: str, user_content: str) -> str:
        """应用功能性模板（使用模块化方式）"""
        # 这里可以实现更复杂的模板应用逻辑
        # 比如将模板保存为临时文件，然后在用户内容中导入
        return template_content + "\n\n" + user_content
    
    def _convert_image_format(self, content: str) -> str:
        """转换图片格式以适配模板"""
        import re
        
        pattern = r'(#image\([^)]*\))\s*;?\s*(?:图\d+\s+([^\s]{1,20}))'
        
        def replace_image(match):
            image_content = match.group(1)
            image_desc = match.group(2) or ""
            image_desc = image_desc.strip()
            
            image_params = image_content[7:-1]
            return f'#reimage(image({image_params}), "{image_desc}"); '
        
        return re.sub(pattern, replace_image, content)
    
    def _convert_table_format(self, content: str) -> str:
        """转换表格格式以适配模板"""
        import re
        
        pattern = r'表(\d+)\s+([^\s]{1,20})\s*(?:[^#]+?#table\(.*?\);)'
        
        def replace_table(match):
            table_num = match.group(1)
            table_desc = match.group(2) or ""
            table_desc = table_desc.strip()
            full_match = match.group(0)
            
            table_start = full_match.find("#table(")
            table_end = full_match.rfind(");") + 1
            table_content = full_match[table_start:table_end]
            
            return f'#retable("{table_desc}", table{table_content[6:-1]})); '
        
        return re.sub(pattern, replace_table, content)
    
    def validate_template(self, template_id: str) -> Tuple[bool, List[str]]:
        """
        验证模板
        
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        template_info = self.get_template_info(template_id)
        if not template_info:
            errors.append(f"模板不存在: {template_id}")
            return False, errors
        
        try:
            # 检查文件是否存在
            template_path = self.templates_dir / template_info.typst_file
            if not template_path.exists():
                errors.append(f"模板文件不存在: {template_path}")
            
            # 检查文件是否可读
            content = self.get_template_content(template_info)
            if not content.strip():
                errors.append("模板文件为空")
            
            # 可以添加更多验证逻辑，比如语法检查等
            
        except Exception as e:
            errors.append(f"模板验证失败: {str(e)}")
        
        return len(errors) == 0, errors
